import { services } from "$src/lib/api/features"
import { redirect } from '@sveltejs/kit'; // Add redirect import;
import type { PageServerLoad } from "./$types";
import { error, fail } from "@sveltejs/kit";
import type { Actions } from "./$types";
// import { departments } from "$src/lib/components/UI/UserAssignDepartment.svelte";

export const load: PageServerLoad = async ({ params, cookies }) => {
    let access_token = cookies.get('access_token');
    let refresh_token = cookies.get('refresh_token');
    const userId = params.id;

    if (!access_token) {
        return {
            users: [],
            partners: [],
            roles: [],
            myTickets: [],
            departments: [],
            tags: [],
            // tickets: [],
            error: 'No access token available'
        };
    }

    for (let repeatCount = 0; repeatCount < 3; repeatCount++) {
        try {
            const userResponse = await services.users.getById(userId, access_token);
            const partnersResponse = await services.companies.getAll(access_token);
            const rolesResponse = await services.roles.getAll(access_token);
            const lineAccountsResponse = await services.line_chat_box.getAllUsers(access_token);
            const departmentResponse = await services.departments.getAll(access_token);
            // const ticketResponse = await services.tickets.get(access_token)
            // const userTicketReponse = await services.users.getUserTickets(userId, access_token) 
            const response_selfUserInfo = await services.users.getUserInfo(access_token);    
            const response_tag = await services.users.getAllTags(access_token)       

            if (userResponse.res_status === 401 || partnersResponse.res_status === 401 || rolesResponse.res_status === 401 || departmentResponse.res_status === 401 || response_selfUserInfo.res_status === 401 ) {
                throw error(401, 'Invalid access token!!!');
            }

            const response_my_tickets = await services.users.getUserTickets(userResponse.users.id, access_token);
            
            if (response_my_tickets.res_status === 401 ) {
                throw error(401, 'Invalid access token!!!');
            }
            
            // console.log(departmentResponse.departments)
            console.log(userResponse)
            // console.log(lineAccountsResponse.users)
            // console.log(response_my_tickets.users.tickets)

            return {
                user: userResponse.users || [],
                lineAccounts: lineAccountsResponse.users || [],
                partners: partnersResponse.partners || [],
                roles: rolesResponse.roles || [],
                myTickets: response_my_tickets.users.tickets,
                departments : departmentResponse.departments || [],
                tags : response_tag.tags || [],
                // tickets: userTicketReponse.users.tickets || [],
                role: response_selfUserInfo.users.roles[0].name,
                loginUser: response_selfUserInfo.users
            };
        } catch (err) {
            // console.error('Error fetching user details:', err);
            // error(500, 'Failed to load user details');

            const refreshResponse = await services.users.refreshToken(refresh_token);
            const login_token = refreshResponse.login_token;

            if (login_token.length === 0) {
                cookies.set("isLogin", 'false', { path: '/' })
                throw redirect(302, '/login');
            } else {
                access_token = login_token.access;
                refresh_token = login_token.refresh;

                cookies.set("access_token", access_token, { path: '/' });
                cookies.set("refresh_token", refresh_token, { path: '/' })
            }
        }
    }

};

export const actions: Actions = {
    update_user: async ({ request, fetch, cookies }) => {
        const access_token = cookies.get('access_token');
        const formData = await request.formData();

        const userId = formData.get('id');
        const userData = {
            username: formData.get('username'),
            name: formData.get('name'),
            work_email: formData.get('work_email'),
            employee_id: formData.get('employee_id'),
            first_name: formData.get('first_name'),
            last_name: formData.get('last_name'),
            // department: formData.get('department'),
            // role: formData.get('role'),
            is_active: formData.get('is_active')

        };

        const response = await services.users.putById(userId, userData, access_token)
        if (response.error_msg) {
            return fail(response.res_status, { error: `${response.error_msg}` });
        }
        let res_msg = response.res_msg;

        return { success: true, res_msg };
    },

    delete_user: async ({ request, fetch, cookies }) => {
        const access_token = cookies.get('access_token');
        const formData = await request.formData();

        const userId = formData.get('id');

        const userData = {
            username: formData.get('username'),
            confirm_username: formData.get('confirm_username'),
        }

        // // TODO - verify matching usernames
        // if (userData.username !== userData.confirm_username) {
        //     return fail(500, { error: `Usernames do not match` }); 
        // }

        const response = await services.users.deleteById(userId, userData, access_token)
        if (response.error_msg) {
            // return fail(500, { error: `${response.error_msg}` }); 
            return fail(response.res_status, { error: `${response.error_msg}` });
        }
        let res_msg = response.res_msg;

        return { success: true, res_msg };
    },

    reactivate_user: async ({ request, fetch, cookies }) => {
        const access_token = cookies.get('access_token');
        const formData = await request.formData();

        const userId = formData.get('id');

        const userData = {
            username: formData.get('username'),
            confirm_username: formData.get('confirm_username'),
        }

        // // TODO - verify matching usernames
        // if (userData.username !== userData.confirm_username) {
        //     return fail(500, { error: `Usernames do not match` }); 
        // }

        const response = await services.users.reactivateById(userId, userData, access_token)
        if (response.error_msg) {
            // return fail(500, { error: `${response.error_msg}` }); 
            return fail(response.res_status, { error: `${response.error_msg}` });
        }
        let res_msg = response.res_msg;

        return { success: true, res_msg };
    },

    assign_user_department: async ({ request, cookies }) => {
        const access_token = cookies.get('access_token');
        const formData = await request.formData();
        const userId = formData.get('id');
    
        // Get all department IDs from the form data
        const departmentIds = formData.get('department_ids[]');
        // Convert the received value to an array of numbers
        const departmentIds_num = departmentIds.toString().split(',').map(num => parseInt(num.trim()));
    
        const departmentData = {
            department_ids: departmentIds_num,
        };
    
        const response = await services.users.assignDepartmentById(userId, departmentData, access_token);
    
        if (response.error_msg) {
            return fail(response.res_status, { error: `${response.error_msg}` });
        }
        let res_msg = response.res_msg;
    
        return { success: true, res_msg };
    },

    assign_user_partner: async ({ request, fetch, cookies }) => {
        const access_token = cookies.get('access_token');
        const formData = await request.formData();
        const userId = formData.get('id');

        // Get all partner IDs from the form data
        const partnerIds = formData.get('partner_ids[]');
        const partnerIds_num = partnerIds.toString().split(',').map(num => parseInt(num.trim()));

        const partnerData = {
            // company_ids: partnerIds_num,
            partner_ids: partnerIds_num,
        }

        // const response = await services.users.assignPartnerById(userId, partnerData, access_token)
        // const response = await services.companies.assignCompanyById(userId, partnerData, access_token)
        const response = await services.users.assignCompanyById(userId, partnerData, access_token)

        if (response.error_msg) {
            return fail(response.res_status, { error: `${response.error_msg}` });
        }
        let res_msg = response.res_msg;

        return { success: true, res_msg };
    },

    remove_user_partner: async ({ request, fetch, cookies }) => {
        const access_token = cookies.get('access_token');
        const formData = await request.formData();
        const userId = formData.get('id');

        // Get all partner IDs from the form data
        const partnerIds = formData.get('partner_ids[]');
        const partnerIds_num = partnerIds.toString().split(',').map(num => parseInt(num.trim()));

        const partnerData = {
            company_ids: partnerIds_num,
        }

        // const response = await services.users.deleteById(userId, userData, access_token)
        // const response = await services.users.removePartnerById(userId, partnerData, access_token)
        const response = await services.companies.removeCompanyById(userId, partnerData, access_token)
        if (response.error_msg) {
            return fail(response.res_status, { error: `${response.error_msg}` });
        }
        let res_msg = response.res_msg;

        return { success: true, res_msg };
    },

    assign_user_role: async ({ request, fetch, cookies }) => {
        const access_token = cookies.get('access_token');
        const formData = await request.formData();
        const userId = formData.get('id');

        // Get all role IDs from the form data
        const roleIds = formData.get('role_ids[]');
        // const roleIds_num = roleIds.toString().split(',').map(num => parseInt(num.trim()));

        const roleData = {
            role_ids: [roleIds],
        }

        const response = await services.users.assignRoleById(userId, roleData, access_token)
        if (response.error_msg) {
            return fail(response.res_status, { error: `${response.error_msg}` });
        }
        let res_msg = response.res_msg;

        return { success: true, res_msg };
    },

    remove_user_role: async ({ request, fetch, cookies }) => {
        const access_token = cookies.get('access_token');
        const formData = await request.formData();
        const userId = formData.get('id');

        // Get all role IDs from the form data
        const roleIds = formData.get('role_ids[]');
        const roleIds_num = roleIds.toString().split(',').map(num => parseInt(num.trim()));

        const roleData = {
            role_ids: roleIds_num,
        }

        const response = await services.users.removeRoleById(userId, roleData, access_token)
        if (response.error_msg) {
            return fail(response.res_status, { error: `${response.error_msg}` });
        }
        let res_msg = response.res_msg;

        return { success: true, res_msg };
    },

    update_user_line_id: async ({ request, fetch, cookies }) => {
        try {
            const access_token = cookies.get('access_token');
            if (!access_token) {
                return fail(401, { error: "Unauthorized: No access token found" });
            }

            const formData = await request.formData();
            const userLineId = formData.get('userLineId') as string;
            const userId = formData.get('id') as string;

            if (!userLineId || !userId) {
                return fail(400, { error: "Bad Request: Missing userLineId or userId" });
            }

            console.log("Updating User Line ID:", userLineId);

            const lineIdData = { line_user_id: userLineId };

            const response = await services.users.updateUserLineAccountId(userId, lineIdData, access_token);

            if (response.error_msg) {
                return fail(response.res_status, { error: response.error_msg });
            }

            return { success: true, res_msg: response.res_msg };

        } catch (error) {
            console.error("Error in update_user_line_id:", error);
            return fail(500, { error: "Internal Server Error: Unable to update user line ID" });
        }
    },

    assign_user_tag: async ({ request, cookies }) => {
        const access_token = cookies.get('access_token');
        const formData = await request.formData();
        const userId = formData.get('id');
    
        // Get all tag IDs from the form data
        const tagIds = formData.get('tag_ids[]');
        // Convert the received value to an array of numbers
        const tagIds_num = tagIds.toString()
            .split(',')
            .map(num => parseInt(num.trim()))
            .filter(num => !isNaN(num));
    
        const tagData = {
            tag_ids: tagIds_num,
        };
    
        const response = await services.users.assignTagsById(userId, tagData, access_token);
    
        if (response.error_msg) {
            return fail(response.res_status, { error: `${response.error_msg}` });
        }
        let res_msg = response.res_msg;
    
        return { success: true, res_msg };
    },

    force_change_password: async ({ request, cookies }) => {
        const access_token = cookies.get('access_token');
        const formData = await request.formData();

        const userId = formData.get('user_id');
        const newPassword = formData.get('new_password');
        const confirmPassword = formData.get('confirm_password');

        // Validate passwords match
        if (newPassword !== confirmPassword) {
            return fail(400, { error: 'Passwords do not match' });
        }

        const passwordData = {
            new_password: newPassword,
            confirm_password: confirmPassword
        };

        const response = await services.users.forceChangePassword(userId, passwordData, access_token);
        
        if (response.error_msg) {
            return fail(response.res_status, { error: `${response.error_msg}` });
        }

        return { 
            success: true, 
            res_msg: response.res_msg || 'Password changed successfully' 
        };
    }
};